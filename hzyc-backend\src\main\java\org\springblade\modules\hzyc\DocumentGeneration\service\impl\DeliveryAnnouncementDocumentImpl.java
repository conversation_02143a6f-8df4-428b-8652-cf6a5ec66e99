package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 送达公告文档生成实现类
 *
 * <AUTHOR>
 */
@Service("deliveryAnnouncementDocument")
public class DeliveryAnnouncementDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "送达公告.docx";
    }

    @Override
    public String getDocumentType() {
        return "DELIVERY-ANNOUNCEMENT";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("XGSJ", "modify_time");
        mapping.put("CFYJ", "legal_basis");
        mapping.put("AFSJ", "case_date");
        mapping.put("SDGGBS", "delivery_announcement_id");
        mapping.put("AFDD", "case_addr");
        mapping.put("SDWSMC", "delivery_doc_name");
        mapping.put("AJMC", "case_name");
        mapping.put("KZZD3", "ext3");
        mapping.put("DWJC", "org_shortname");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("WSRQ", "doc_date");
        mapping.put("FBLJ", "publish_url");
        mapping.put("WSZT", "doc_status");
        mapping.put("XTCJSJCXBYDX", "sys_create_time");
        mapping.put("ND", "year");
        mapping.put("FBZT", "publish_status");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_update");
        mapping.put("CJSJ", "create_time");
        mapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
        mapping.put("BZ", "remark");
        mapping.put("AJBS", "case_uuid");
        mapping.put("KZZD1", "ext1");
        mapping.put("SJBM", "city_org_code");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("SFYX", "is_active");
        mapping.put("CFJD", "punish_decide");
        mapping.put("SDWSBH", "delivery_doc_no");
        mapping.put("XTGXSJCXBYDX", "sys_modify_time");
        mapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
        mapping.put("DSR", "party");
        mapping.put("KZZD2", "ext2");
        mapping.put("FBSJ", "publish_time");
        mapping.put("WSH", "doc_no");
        mapping.put("DWSX", "org_abbr");
        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_sync_delete");
        mapping.put("SJMC", "city_org_name");
        mapping.put("CJR", "creator");
        mapping.put("XGR", "modifier");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1){
            Map<String, Object> query = new HashMap<>();

            // query.put("AJBS", "24909616d4b042d8bb4b7e693382e9bb");
            JSONArray array = icaseInfoService.getDeliveryAnnouncementDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("industry_unique_id", "example_tid_001");
        mockData.put("delivery_announcement_id", "sdgg_001");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟处﹝2025﹞第48号");
        mockData.put("doc_date", "2025/6/10");
        mockData.put("year", "2025");

        // 送达文书信息
        mockData.put("delivery_doc_name", "行政处罚决定书");
        mockData.put("delivery_doc_no", "SDGG-2025-001");
        mockData.put("doc_no", "博烟处﹝2025﹞第48号");

        // 案件信息
        mockData.put("case_name", "梁俊强未在当地烟草专卖批发企业进货案");
        mockData.put("case_date", "2025/3/18");
        mockData.put("case_addr", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("party", "梁俊强");

        // 处罚决定
        mockData.put("punish_decide", "处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元，上缴国库。");
        mockData.put("legal_basis", "《中华人民共和国烟草专卖法实施条例》第五十六条");

        // 发布信息
        mockData.put("publish_time", "2025/6/15");
        mockData.put("publish_url", "http://example.com/announcement");
        mockData.put("publish_status", 1);
        mockData.put("doc_status", "已发布");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");

        // 其他信息
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("org_abbr", "博罗烟草");
        mockData.put("mc_tec_ctime", "2025/6/11 1:03");
        mockData.put("service_center_sync_update", "");
        mockData.put("service_center_sync_delete", 0);

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("remark", "");

        return mockData;
    }
}
