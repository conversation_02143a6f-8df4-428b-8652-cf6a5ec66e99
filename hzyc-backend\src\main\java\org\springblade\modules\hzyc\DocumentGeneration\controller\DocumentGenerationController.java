package org.springblade.modules.hzyc.DocumentGeneration.controller;


import org.springblade.core.mp.support.Condition;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.WordDocumentService;
import org.springblade.modules.hzyc.DocumentGeneration.service.DeliveryAnnouncementService;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springblade.modules.hzyc.event.pojo.entity.EventEntity;
import org.springblade.modules.hzyc.event.service.IEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springblade.core.oss.model.BladeFile;
import java.io.ByteArrayInputStream;

@RestController
@RequestMapping("/blade-case/documents")
public class DocumentGenerationController {

    @Autowired
    private WordDocumentService wordDocumentService;
	@Autowired
	private IDocumentService documentService;
	@Autowired
	private IEventService eventService;

    @Value("${blade.file.remote-path:/tmp}")
    private String tempFilePath;

    @Value("${blade.file.upload-domain:http://localhost:8080}")
    private String uploadDomain;

    @Autowired
    private Map<String, DocumentGenerator> documentGenerators;
    // 删除 getDocumentGenerator 方法，因为现在使用单独的实现类

    @Autowired
    private OssBuilder ossBuilder;

    /**
     * 生成案卷文档
     * @param templateName 模板名称
     * @param caseId 案件ID
     * @param mode 当mode值为update时，即为用户在页面编辑了数据，需重新生成内容
     * @return 生成的文档信息
     */
    @GetMapping("/generate")
    public ResponseEntity<Object> generateDocument(
            @RequestParam(value = "mode") String mode,
            @RequestParam(value = "fileId", required = false) String fileId,
            @RequestParam(value = "documentType") String documentType,
            @RequestParam(value = "templateName", required = false) String templateName,
            @RequestParam(value = "caseId", required = false) String caseId ) {
        try {
            // 转换documentType格式：kebab-case -> camelCase
            String camelCaseDocumentType = convertToCamelCase(documentType);
            DocumentGenerator generator = documentGenerators.get(camelCaseDocumentType + "Document");
            if (generator == null) {
                throw new IllegalArgumentException("不支持的文档类型: " + documentType);
            }

            // 获取原始数据
            Map<String, Object> rawData = getTestData();

            // 使用生成器处理数据
            List<Map<String, Object>> data = generator.processData(rawData, caseId,mode,fileId);

            // 使用生成器的模板名称（如果没有指定）
            String finalTemplateName = templateName != null ? templateName : generator.getTemplateName();

            // 防重复数据
            Arrays.stream(documentType.split(",")).collect(Collectors.toList()).forEach(type -> {
                          DocumentEntity query = new DocumentEntity();
                query.setCaseUuid(caseId);
                query.setDocumentType(type);
                List<DocumentEntity> result = documentService.list(Condition.getQueryWrapper(query));
                if (!result.isEmpty()) {
                    for(DocumentEntity item: result ){
                        documentService.removeById(item);
                    }

                }
            });

            for(Map<String, Object> item : data){
                // 生成文档
                byte[] document = wordDocumentService.generateDocument(finalTemplateName, item);

                // 上传文件到MinIO
                String fileName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "_" + UUID.randomUUID().toString().substring(0, 8) + "_" + finalTemplateName + ".docx";
                BladeFile bladeFile = ossBuilder.template().putFile(fileName, new ByteArrayInputStream(document));
                // 文档表保存数据
                Arrays.stream(documentType.split(",")).collect(Collectors.toList()).forEach(type -> {
                    DocumentEntity documentEntity = new DocumentEntity();
                    documentEntity.setCaseUuid(caseId);
                    documentEntity.setDocumentType(type);
                    documentEntity.setDocumentContent(JsonUtil.toJson(item));
                    documentEntity.setFileUrl(bladeFile.getLink()); // 保存MinIO文件路径
                    documentService.saveOrUpdate(documentEntity);
                });

            }

            // 插入操作记录到ca_event表
            try {
                EventEntity eventEntity = new EventEntity();
                eventEntity.setEventType("DOCUMENT_GENERATION");
                eventEntity.setContent(String.format("案件[%s]生成文档类型[%s]，模板[%s]", caseId, documentType, finalTemplateName));
                eventService.save(eventEntity);
            } catch (Exception eventException) {
                // 记录事件失败不影响主流程，只记录日志
                eventException.printStackTrace();
            }

            // 返回文件信息的JSON响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "文档生成成功");
            response.put("caseId", caseId);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "文档生成失败: " + e.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取已生成的文档
     */
    @GetMapping("/view/{fileName}")
    public ResponseEntity<byte[]> viewGeneratedDocument(@PathVariable String fileName) {
        try {
            Path filePath = Paths.get(tempFilePath, "generated_docs", fileName);
            if (!Files.exists(filePath)) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            byte[] fileContent = Files.readAllBytes(filePath);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);

            return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取固定的测试数据
     */
    private Map<String, Object> getTestData() {
        Map<String, Object> data = new HashMap<>();

        // 使用您提供的测试数据（已转换为驼峰格式）
        data.put("invstgRptUuid", "8a83cd9171f2512f017207d27b255fae");

        return data;
    }
    /**
     * 将kebab-case格式转换为camelCase格式
     * 例如：case-filing-report -> caseFilingReport
     */
    private String convertToCamelCase(String kebabCase) {
        if (kebabCase == null || kebabCase.isEmpty()) {
            return kebabCase;
        }

        String[] parts = kebabCase.split("-");
        StringBuilder camelCase = new StringBuilder(parts[0]);

        for (int i = 1; i < parts.length; i++) {
            if (!parts[i].isEmpty()) {
                camelCase.append(Character.toUpperCase(parts[i].charAt(0)))
                         .append(parts[i].substring(1));
            }
        }

        return camelCase.toString();
    }
}



