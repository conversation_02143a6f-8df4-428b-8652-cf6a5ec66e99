package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 行政处罚决定书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("penaltyDecisionDocument")
public class PenaltyDecisionDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public  List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId,String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
            // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
		System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "41行政处罚决定书.docx";
    }

    @Override
    public String getDocumentType() {
        return "PENALTY-DECISION";
    }

	public static Map<String, String> getReverseFieldMapping() {
		Map<String, String> mapping = new HashMap<>();

		mapping.put("FLTK", "legal_info");
		mapping.put("DSR_NAME", "party_name");
		mapping.put("XKZH", "lice_no");
		mapping.put("AJXZ", "case_prop");
		mapping.put("JKDH", "notice_no");
		mapping.put("WFCD", "bcb_enum_legal_level");
		mapping.put("XGSX", "other_info");
		mapping.put("KZZD1", "ext1");
		mapping.put("ZJNR", "evidence_content");
		mapping.put("TYZDTS", "stop_rectify_days");
		mapping.put("XZFYDW", "recheck_org");
		mapping.put("AJMC", "case_name");
		mapping.put("SY", "punish_cause");
		mapping.put("SZSJBM", "city_org_code");
		mapping.put("GXSJ", "dc_tec_utime");
		mapping.put("DSRID", "party_uuid");
		mapping.put("SJSSDW", "own_org_uuid");
		mapping.put("WSHQ", "full_doc_no");
		mapping.put("FBSJ", "publish_time");
		mapping.put("XYWYBS", "tid");
		mapping.put("XTGXSJ", "sys_modify_time");
		mapping.put("FWZXSJGXSJ", "sysupdatetime");
		mapping.put("CBBMUUID", "reg_dept_uuid");
		mapping.put("CJSJ", "create_time");
		mapping.put("JDSBS", "decide_uuid");
		mapping.put("DWJC", "org_shortname");
		mapping.put("SSFY", "court_name");
		mapping.put("AFDD", "case_addr");
		mapping.put("SFJQCF", "is_mitigate_punish");
		mapping.put("ZFRY", "undertaker");
		mapping.put("MSK", "confiscate_amt");
		mapping.put("SGJE", "buy_amt");
		mapping.put("AFRQ", "case_date");
		mapping.put("GGRQ", "doc_notice_date");
		mapping.put("SFYX", "is_active");
		mapping.put("DSRJBXX", "party_info");
		mapping.put("SFDX", "is_cancel");
		mapping.put("AJBS", "case_uuid");
		mapping.put("ZZJGUUID", "org_uuid");
		mapping.put("XGSJ", "modify_time");
		mapping.put("XTCJSJ", "sys_create_time");
		mapping.put("JKJG", "payed_result");
		mapping.put("CFJD", "punish_decide");
		mapping.put("RKSJ", "dc_tec_ctime");
		mapping.put("ZLBS", "dc_tec_operation");
		mapping.put("DSR", "party");
		mapping.put("JGSXZ", "org_abbr");
		mapping.put("KZZD3", "ext3");
		mapping.put("JKDH_INC", "notice_no_inc");
		mapping.put("CFZL", "punish_type");
		mapping.put("CJR", "creator");
		mapping.put("JDSLX", "decide_type");
		mapping.put("XGR", "modifier");
		mapping.put("TAR", "same_party");
		mapping.put("WFSS", "legal_fact");
		mapping.put("FWZXSJSCBJ", "sysisdelete");
		mapping.put("LASJ", "register_time");
		mapping.put("FKJE", "forfeit");
		mapping.put("SJSSBM", "own_dept_uuid");
		mapping.put("MCRKSJ", "mc_tec_ctime");
		mapping.put("AJBH", "case_code");
		mapping.put("CHBMUUID", "get_dept_uuid");
		mapping.put("JKSJ", "pay_time");
		mapping.put("SZSJMC", "city_org_name");
		mapping.put("KZZD2", "ext2");
		mapping.put("FBZT", "publish_status");
		mapping.put("WSRQ", "doc_date");
		mapping.put("FBLJ", "publish_url");
		mapping.put("JGJC", "org_short_name");

		return mapping;
	}

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type,String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type==1){
            Map<String, Object> query=new HashMap<>();

//            query.put("AJBS", "24909616d4b042d8bb4b7e693382e9bb");
            JSONArray array = icaseInfoService.getAdminPenaltyDecisionDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
				Map<String, Object> processData = new HashMap<>();
				Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
					// 处理数据
					firstData.forEach((key, value) -> {
						String newKey = mapper.get(key);
						if (StrUtil.isBlank(newKey)) {
							newKey = key;
						}
						processData.put(newKey, value);
					});
                    return processData;
                }
            }
        }



        // 基础信息
        mockData.put("decide_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("decide_type", "1");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟处﹝2025﹞第48号");
        mockData.put("doc_date", "2025/6/10");

        // 当事人信息
        mockData.put("party_uuid", "");
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：************，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 案件信息
        mockData.put("case_date", "");
        mockData.put("case_addr", "");
        mockData.put("register_time", "");
        mockData.put("case_prop", "未在当地烟草专卖批发企业进货");

        // 违法事实
        mockData.put("legal_fact", "2025年03月18日， 我局接群众举报称位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号的\"博罗县龙溪隆胜轩茶烟酒商行\"有涉嫌违法经营卷烟的行为，接报后我局马上组织人员到现场进行检查。我局专卖执法人员叶辉明(19090352015),朱兆强(19090352023)经出示执法证件，表明身份，说明来意后，依法对位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号\"博罗县龙溪隆胜轩茶烟酒商行\"（烟草专卖零售许可证号：************）的经营场所进行检查，在该场所内发现涉嫌违法的烟草专卖品黄果树(长征)200条、白沙(硬精品三代)150条、红塔山(硬经典)150条、黄山(新一品)100条、娇子(蓝时代)100条、白沙(硬)75条、天子(金)25条、金圣(硬滕王阁)25条、黄金叶(喜满堂)25条、双喜(硬经典1906)25条、金圣(庐山)25条、大前门(软)25条、黄山(印象一品)25条、黄鹤楼(硬金砂)25条、红旗渠(新版银河)25条、双喜(莲香)25条、芙蓉王(硬)50条，合计17个品种1075条。");

        // 证据内容
        mockData.put("evidence_content", "以上事实有以下证据予以证实：1、涉案卷烟共17个品牌规格合计1075条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单（含提取当事人梁俊强身份证、烟草专卖零售许可证复印件各1份，涉案店铺、涉案卷烟、被我局先行登记保存卷烟照片各1张，涉案卷烟32位码详情单1份，违法记录2份）；4、《询问笔录》1份；5、《现场笔录》1份；6、《卷烟、雪茄烟鉴别检验报告》1份； 7、《涉案物品核价表》1份。");

        // 处罚决定
        mockData.put("punish_decide", "依据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，并遵照《广东省烟草专卖行政处罚裁量权管理办法》第十一条第（三）项及附件1 的处罚幅度，我局对当事人作出从重处罚决定，行政处罚如下：处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元，上缴国库。");

        // 金额信息
        mockData.put("forfeit", 10319.37);
        mockData.put("confiscate_amt", 0);
        mockData.put("buy_amt", 0);

        // 其他信息
        mockData.put("lice_no", "");
        mockData.put("is_cancel", 0);
        mockData.put("bcb_enum_legal_level", "");
        mockData.put("undertaker", "");
        mockData.put("doc_notice_date", "");
        mockData.put("recheck_org", "");
        mockData.put("court_name", "");

        // 其他信息和法律信息
        mockData.put("other_info", "本行政处罚决定书一经送达即生效，限你（单位）在收到本处罚决定书之日起15日内履行本决定。同时到指定的银行或通过电子支付系统缴纳罚款。若逾期不按规定缴纳罚款，每日按罚款额的3%加处罚款。如不服本行政处罚决定，可在收到本行政处罚决定书之日起60天内，向广东省惠州市烟草专卖局或博罗县人民政府申请行政复议；或依法于15日内向惠州市惠城区人民法院提起行政诉讼，行政复议或行政诉讼期间，本处罚决定不停止执行。逾期不申请复议或不提起行政诉讼又不履行处罚的，本局将申请人民法院强制执行。");

        mockData.put("legal_info", "《中华人民共和国烟草专卖法实施条例》第五十六条：取得烟草专卖零售许可证的企业或者个人违反本条例第二十三条第二款的规定，未在当地烟草专卖批发企业进货的，由烟草专卖行政主管部门没收违法所得，可处以进货总额5%以上10%以下的罚款。《广东省烟草专卖行政处罚裁量权管理办法》第十一条第（三）项：有下列情形之一的，应当从重处罚：……（三）三次以上实施涉烟违法行为的;……");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sysisdelete", "");
        mockData.put("sysupdatetime", "");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");

        // 处罚相关
        mockData.put("punish_type", "02");
        mockData.put("notice_no_inc", "220889");
        mockData.put("notice_no", "0120250610220889");
        mockData.put("payed_result", -1);
        mockData.put("pay_time", "");
        mockData.put("punish_cause", "");
        mockData.put("is_mitigate_punish", 0);
        mockData.put("stop_rectify_days", "");
        mockData.put("tid", "");
        mockData.put("publish_time", "");
        mockData.put("publish_url", "");
        mockData.put("publish_status", "");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("dc_tec_utime", "");
        mockData.put("dc_tec_ctime", "2025/6/11 1:03");
        mockData.put("dc_tec_operation", "2025/6/11 1:03");
        mockData.put("mc_tec_ctime", "I");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}

